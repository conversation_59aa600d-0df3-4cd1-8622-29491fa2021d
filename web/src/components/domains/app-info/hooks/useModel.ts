import * as React from 'react';

type useModalReturnType = [
  open: boolean,
  showModal: () => void,
  hideModal: () => void,
];

const useModal = (): useModalReturnType => {
  const [open, setOpen] = React.useState(false);

  const showModal = React.useCallback(() => {
    console.log('pass2');
    setOpen(true);
  }, []);

  const hideModal = React.useCallback(() => {
    setOpen(false);
  }, []);

  return [open, showModal, hideModal];
};

export default useModal;
